// Global type definitions for the Electron application

import { ApiResponse } from './common';
import { Building, BuildingWithStats } from './Building';
import { Floor } from './Floor';
import { Apartment, ApartmentWithDetails, ApartmentBookingHistory } from './Apartment';
import { Customer, CustomerWithCurrentApartment, CreateCustomerRequest, UpdateCustomerRequest } from './Customer';

// Define the electronAPI interface
interface ElectronAPI {
  // Database operations
  database: {
    init: () => Promise<ApiResponse<void>>;
    query: (sql: string, params?: any[]) => Promise<ApiResponse<any[]>>;
    execute: (sql: string, params?: any[]) => Promise<ApiResponse<any>>;
  };

  // Building operations
  buildings: {
    create: (building: Omit<Building, 'id' | 'createdAt' | 'updatedAt'>) => Promise<ApiResponse<Building>>;
    update: (building: Partial<Building> & { id: number }) => Promise<ApiResponse<Building>>;
    delete: (id: number) => Promise<ApiResponse<void>>;
    getAll: () => Promise<ApiResponse<Building[]>>;
    getById: (id: number) => Promise<ApiResponse<Building>>;
    getWithStats: () => Promise<ApiResponse<BuildingWithStats[]>>;
  };

  // Floor operations
  floors: {
    create: (floor: Omit<Floor, 'id' | 'createdAt' | 'updatedAt'>) => Promise<ApiResponse<Floor>>;
    update: (floor: Partial<Floor> & { id: number }) => Promise<ApiResponse<Floor>>;
    delete: (id: number) => Promise<ApiResponse<void>>;
    getByBuilding: (buildingId: number) => Promise<ApiResponse<Floor[]>>;
    autoGenerate: (buildingId: number, floors: number, apartmentsPerFloor: number) => Promise<ApiResponse<Floor[]>>;
  };

  // Apartment operations
  apartments: {
    create: (apartment: Omit<Apartment, 'id' | 'createdAt' | 'updatedAt'>) => Promise<ApiResponse<Apartment>>;
    update: (apartment: Partial<Apartment> & { id: number }) => Promise<ApiResponse<Apartment>>;
    delete: (id: number) => Promise<ApiResponse<void>>;
    getByFloor: (floorId: number) => Promise<ApiResponse<Apartment[]>>;
    getWithDetails: () => Promise<ApiResponse<ApartmentWithDetails[]>>;
    book: (apartmentId: number, customerId: number, startDate: string, rent: number, advance: number) => Promise<ApiResponse<void>>;
    checkout: (apartmentId: number) => Promise<ApiResponse<void>>;
    getHistory: (apartmentId: number) => Promise<ApiResponse<ApartmentBookingHistory[]>>;
  };

  // Customer operations
  customers: {
    create: (customer: CreateCustomerRequest) => Promise<ApiResponse<Customer>>;
    update: (customer: UpdateCustomerRequest) => Promise<ApiResponse<Customer>>;
    delete: (id: number) => Promise<ApiResponse<void>>;
    getAll: () => Promise<ApiResponse<CustomerWithCurrentApartment[]>>;
    getById: (id: number) => Promise<ApiResponse<CustomerWithCurrentApartment>>;
    getAvailable: () => Promise<ApiResponse<Customer[]>>;
    getHistory: (customerId: number) => Promise<ApiResponse<ApartmentBookingHistory[]>>;
  };

  // File operations
  files: {
    upload: (fileData: ArrayBuffer, fileName: string, entityType: string, entityId: number, fieldName: string) => Promise<ApiResponse<{ filePath: string }>>;
    delete: (filePath: string) => Promise<ApiResponse<void>>;
    getMetadata: (filePath: string) => Promise<ApiResponse<any>>;
    generateThumbnail: (filePath: string) => Promise<ApiResponse<string>>;
  };

  // Google Drive operations
  google: {
    auth: () => Promise<ApiResponse<void>>;
    sync: () => Promise<ApiResponse<void>>;
    upload: (filePath: string) => Promise<ApiResponse<string>>;
    download: (fileId: string) => Promise<ApiResponse<string>>;
  };

  // User operations
  users: {
    create: (user: any) => Promise<ApiResponse<any>>;
    update: (user: any) => Promise<ApiResponse<any>>;
    getCurrent: () => Promise<ApiResponse<any>>;
  };

  // Search and reporting
  search: {
    global: (query: string) => Promise<ApiResponse<any[]>>;
  };

  reports: {
    generate: (type: string, params: any) => Promise<ApiResponse<any>>;
  };

  // Data export/import
  export: {
    data: (type: string) => Promise<ApiResponse<string>>;
  };

  import: {
    data: (filePath: string) => Promise<ApiResponse<void>>;
  };
}

// Extend the global Window interface
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
