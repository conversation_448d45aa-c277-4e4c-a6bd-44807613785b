import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Breadcrumbs,
  Link,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  Layers as LayersIcon,
  Home as HomeIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon,
  CalendarToday as CalendarIcon,
  History as HistoryIcon,
  NavigateNext as NavigateNextIcon,
  CheckCircle as OccupiedIcon,
  RadioButtonUnchecked as AvailableIcon,
  ExitToApp as CheckoutIcon,
  PersonAdd as BookIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { ApartmentWithDetails, ApartmentBookingHistory } from '../../types/Apartment';
import { ApartmentService } from '../services/apartmentService';
import { useNotification } from '../contexts/NotificationContext';

const ApartmentDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  const [apartment, setApartment] = useState<ApartmentWithDetails | null>(null);
  const [bookingHistory, setBookingHistory] = useState<ApartmentBookingHistory[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadApartmentDetails();
    }
  }, [id]);

  const loadApartmentDetails = async () => {
    try {
      setLoading(true);

      // Get apartment details from the database
      const apartmentResponse = await ApartmentService.getApartmentWithDetails(parseInt(id!));

      if (!apartmentResponse.success) {
        showError(apartmentResponse.error || 'Failed to load apartment details');
        return;
      }

      const aptData = apartmentResponse.data;

      // Transform the database response to match our interface
      const transformedApartment: ApartmentWithDetails = {
        id: aptData.id,
        name: aptData.name,
        currentRate: aptData.current_rate,
        size: aptData.size,
        numberOfRooms: aptData.number_of_rooms,
        floorId: aptData.floor_id,
        currentCustomerId: aptData.current_customer_id,
        isOccupied: aptData.is_occupied === 1,
        createdAt: new Date(aptData.created_at),
        updatedAt: new Date(aptData.updated_at),
        floor: {
          id: aptData.floor_id,
          name: aptData.floor_name,
          floorNumber: aptData.floor_number,
          buildingId: aptData.building_id || 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        building: {
          id: aptData.building_id || 1,
          name: aptData.building_name || 'Unknown Building',
          address: aptData.building_address || 'Unknown Address',
          ownerName: 'Unknown Owner',
          ownerMobileNo: '',
          ownerAddress: '',
          rentAmount: 0,
          advance: 0,
          numberOfFloor: 1,
          agreementDate: new Date(),
          handOverFromOwner: new Date(),
          handOverToOwner: undefined,
          conditions: '',
          comments: '',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        currentCustomer: aptData.customer_name ? {
          id: aptData.current_customer_id,
          name: aptData.customer_name,
          address: aptData.customer_address || '',
          mobile: aptData.customer_mobile || '',
          occupation: aptData.customer_occupation || '',
          startDate: new Date(aptData.customer_start_date || Date.now()),
          rent: aptData.customer_rent || aptData.current_rate,
          advance: aptData.customer_advance || 0,
          isActive: aptData.customer_is_active === 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        } : undefined,
      };

      // Get booking history from the database
      const historyResponse = await ApartmentService.getApartmentHistory(parseInt(id!));

      let transformedHistory: ApartmentBookingHistory[] = [];
      if (historyResponse.success && historyResponse.data) {
        transformedHistory = historyResponse.data.map((booking: any) => ({
          id: booking.id,
          apartmentId: booking.apartment_id,
          customerId: booking.customer_id,
          customer: {
            id: booking.customer_id,
            name: booking.customer_name,
            address: booking.customer_address || '',
            mobile: booking.customer_mobile || '',
            occupation: booking.customer_occupation || '',
            startDate: new Date(booking.start_date),
            rent: booking.rent_amount,
            advance: booking.advance_amount,
            isActive: !booking.end_date,
            createdAt: new Date(booking.created_at),
            updatedAt: new Date(booking.updated_at),
          },
          startDate: new Date(booking.start_date),
          endDate: booking.end_date ? new Date(booking.end_date) : undefined,
          rentAmount: booking.rent_amount,
          advanceAmount: booking.advance_amount,
          checkoutReason: booking.checkout_reason,
          createdAt: new Date(booking.created_at),
          updatedAt: new Date(booking.updated_at),
        }));
      }

      setApartment(transformedApartment);
      setBookingHistory(transformedHistory);
    } catch (error) {
      showError('Failed to load apartment details');
      console.error('Error loading apartment details:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // TODO: Implement edit functionality
    showSuccess('Edit functionality will be implemented');
  };

  const handleDelete = () => {
    // TODO: Implement delete functionality
    showSuccess('Delete functionality will be implemented');
  };

  const handleBook = () => {
    // TODO: Implement booking functionality
    showSuccess('Booking functionality will be implemented');
  };

  const handleCheckout = () => {
    // TODO: Implement checkout functionality
    showSuccess('Checkout functionality will be implemented');
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number): string => {
    return ApartmentService.formatCurrency(amount);
  };

  if (!id) {
    return (
      <Container maxWidth="xl">
        <Alert severity="error">Apartment ID is required</Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!apartment) {
    return (
      <Container maxWidth="xl">
        <Alert severity="error">Apartment not found</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      {/* Breadcrumbs */}
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />}>
          <Link
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigate('/buildings');
            }}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Buildings
          </Link>
          <Link
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigate(`/buildings/${apartment.building.id}/floors`);
            }}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <LayersIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            {apartment.building.name}
          </Link>
          <Link
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigate(`/floors/${apartment.floor.id}/apartments`);
            }}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            {apartment.floor.name}
          </Link>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            {apartment.name}
          </Typography>
        </Breadcrumbs>
      </Box>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
              Apartment {apartment.name}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {apartment.floor.name} • {apartment.building.name}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={handleEdit}
            >
              Edit
            </Button>
            {apartment.isOccupied ? (
              <Button
                variant="contained"
                color="warning"
                startIcon={<CheckoutIcon />}
                onClick={handleCheckout}
              >
                Checkout
              </Button>
            ) : (
              <Button
                variant="contained"
                color="success"
                startIcon={<BookIcon />}
                onClick={handleBook}
              >
                Book Apartment
              </Button>
            )}
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDelete}
            >
              Delete
            </Button>
          </Box>
        </Box>

        {/* Status Chip */}
        <Chip
          icon={apartment.isOccupied ? <OccupiedIcon /> : <AvailableIcon />}
          label={apartment.isOccupied ? 'Occupied' : 'Available'}
          color={apartment.isOccupied ? 'success' : 'default'}
          size="medium"
          sx={{ fontWeight: 600 }}
        />
      </Box>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Apartment Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Apartment Information
              </Typography>
              <List>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <HomeIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Apartment Name"
                    secondary={apartment.name}
                  />
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'secondary.main' }}>
                      <MoneyIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Monthly Rent"
                    secondary={formatCurrency(apartment.currentRate)}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Size"
                    secondary={apartment.size}
                    sx={{ pl: 7 }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Number of Rooms"
                    secondary={`${apartment.numberOfRooms} ${apartment.numberOfRooms === 1 ? 'Room' : 'Rooms'}`}
                    sx={{ pl: 7 }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'info.main' }}>
                      <CalendarIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Created"
                    secondary={formatDate(apartment.createdAt)}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Current Customer Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Current Tenant
              </Typography>
              {apartment.isOccupied && apartment.currentCustomer ? (
                <List>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'success.main' }}>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="Name"
                      secondary={apartment.currentCustomer.name}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Mobile"
                      secondary={apartment.currentCustomer.mobile}
                      sx={{ pl: 7 }}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Occupation"
                      secondary={apartment.currentCustomer.occupation}
                      sx={{ pl: 7 }}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Address"
                      secondary={apartment.currentCustomer.address}
                      sx={{ pl: 7 }}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Start Date"
                      secondary={formatDate(apartment.currentCustomer.startDate)}
                      sx={{ pl: 7 }}
                    />
                  </ListItem>
                </List>
              ) : (
                <Alert severity="info">
                  This apartment is currently available for rent.
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Booking History */}
      <Box sx={{ mt: 4 }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, display: 'flex', alignItems: 'center' }}>
              <HistoryIcon sx={{ mr: 1 }} />
              Booking History
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {bookingHistory.length > 0 ? (
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Customer</TableCell>
                      <TableCell>Start Date</TableCell>
                      <TableCell>End Date</TableCell>
                      <TableCell>Rent Amount</TableCell>
                      <TableCell>Advance</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Checkout Reason</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {bookingHistory.map((booking) => (
                      <TableRow key={booking.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 600 }}>
                              {booking.customer.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {booking.customer.mobile}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{formatDate(booking.startDate)}</TableCell>
                        <TableCell>
                          {booking.endDate ? formatDate(booking.endDate) : (
                            <Chip label="Current" color="success" size="small" />
                          )}
                        </TableCell>
                        <TableCell>{formatCurrency(booking.rentAmount)}</TableCell>
                        <TableCell>{formatCurrency(booking.advanceAmount)}</TableCell>
                        <TableCell>
                          <Chip
                            label={booking.endDate ? 'Completed' : 'Active'}
                            color={booking.endDate ? 'default' : 'success'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {booking.checkoutReason || (booking.endDate ? 'N/A' : '-')}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">
                No booking history available for this apartment.
              </Alert>
            )}
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default ApartmentDetailsPage;
