import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  InputAdornment,
  Fab,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { Building, BuildingWithStats, CreateBuildingRequest, UpdateBuildingRequest } from '../../types/Building';
import { BuildingService } from '../services/buildingService';
import { useNotification } from '../contexts/NotificationContext';
import BuildingForm from '../components/forms/BuildingForm';
import BuildingCard from '../components/cards/BuildingCard';

const BuildingsPage: React.FC = () => {
  const { showSuccess, showError } = useNotification();
  const [buildings, setBuildings] = useState<BuildingWithStats[]>([]);
  const [filteredBuildings, setFilteredBuildings] = useState<BuildingWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [formOpen, setFormOpen] = useState(false);
  const [editingBuilding, setEditingBuilding] = useState<Building | undefined>();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [buildingToDelete, setBuildingToDelete] = useState<Building | undefined>();
  const [searchQuery, setSearchQuery] = useState('');
  const [formLoading, setFormLoading] = useState(false);

  useEffect(() => {
    loadBuildings();
  }, []);

  useEffect(() => {
    filterBuildings();
  }, [buildings, searchQuery]);

  const loadBuildings = async () => {
    try {
      setLoading(true);

      // Get buildings with stats from the database
      const buildingsResponse = await BuildingService.getBuildingsWithStats();

      if (!buildingsResponse.success) {
        showError(buildingsResponse.error || 'Failed to load buildings');
        return;
      }

      const buildingsData = buildingsResponse.data || [];

      // Transform the database response to match our interface
      const transformedBuildings: BuildingWithStats[] = buildingsData.map((building: any) => ({
        id: building.id,
        name: building.name,
        address: building.address,
        ownerName: building.owner_name,
        ownerMobileNo: building.owner_mobile_no,
        ownerAddress: building.owner_address,
        rentAmount: building.rent_amount,
        advance: building.advance,
        numberOfFloor: building.number_of_floor,
        agreementDate: new Date(building.agreement_date),
        handOverFromOwner: new Date(building.hand_over_from_owner),
        handOverToOwner: building.hand_over_to_owner ? new Date(building.hand_over_to_owner) : undefined,
        conditions: building.conditions,
        comments: building.comments,
        createdAt: new Date(building.created_at),
        updatedAt: new Date(building.updated_at),
        totalFloors: building.total_floors || 0,
        totalApartments: building.total_apartments || 0,
        occupiedApartments: building.occupied_apartments || 0,
        availableApartments: building.available_apartments || 0,
        totalRentCollection: building.total_rent_collection || 0,
      }));

      setBuildings(transformedBuildings);
    } catch (error) {
      showError('Failed to load buildings');
      console.error('Error loading buildings:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterBuildings = () => {
    if (!searchQuery.trim()) {
      setFilteredBuildings(buildings);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = buildings.filter(building =>
      building.name.toLowerCase().includes(query) ||
      building.address.toLowerCase().includes(query) ||
      building.ownerName.toLowerCase().includes(query)
    );
    setFilteredBuildings(filtered);
  };

  const handleCreateBuilding = () => {
    setEditingBuilding(undefined);
    setFormOpen(true);
  };

  const handleEditBuilding = (building: Building) => {
    setEditingBuilding(building);
    setFormOpen(true);
  };

  const handleDeleteBuilding = (building: Building) => {
    setBuildingToDelete(building);
    setDeleteDialogOpen(true);
  };

  const handleViewBuilding = (building: Building) => {
    // Navigation is handled in the BuildingCard component
    console.log('Viewing building:', building.name);
  };

  const handleFormSubmit = async (data: CreateBuildingRequest | UpdateBuildingRequest) => {
    try {
      setFormLoading(true);

      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      if ('id' in data) {
        showSuccess('Building updated successfully');
      } else {
        showSuccess('Building created successfully');
      }

      setFormOpen(false);
      await loadBuildings();
    } catch (error) {
      showError('Failed to save building');
      console.error('Error saving building:', error);
    } finally {
      setFormLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (!buildingToDelete) return;

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      showSuccess('Building deleted successfully');
      setDeleteDialogOpen(false);
      setBuildingToDelete(undefined);
      await loadBuildings();
    } catch (error) {
      showError('Failed to delete building');
      console.error('Error deleting building:', error);
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  return (
    <Container maxWidth="xl">
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
              Buildings Management
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage your buildings and their properties
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Tooltip title="Refresh">
              <span>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={loadBuildings}
                  disabled={loading}
                >
                  Refresh
                </Button>
              </span>
            </Tooltip>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateBuilding}
              size="large"
            >
              Add Building
            </Button>
          </Box>
        </Box>

        {/* Search Bar */}
        <TextField
          fullWidth
          placeholder="Search buildings by name, address, or owner..."
          value={searchQuery}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ maxWidth: 500 }}
        />
      </Box>

      {/* Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      ) : filteredBuildings.length === 0 ? (
        <Box sx={{ textAlign: 'center', mt: 4 }}>
          {searchQuery ? (
            <Alert severity="info">
              No buildings found matching "{searchQuery}". Try adjusting your search terms.
            </Alert>
          ) : (
            <Alert severity="info">
              No buildings found. Click "Add Building" to create your first building.
            </Alert>
          )}
        </Box>
      ) : (
        <>
          {/* Statistics Summary */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Summary: {filteredBuildings.length} building{filteredBuildings.length !== 1 ? 's' : ''} found
            </Typography>
          </Box>

          {/* Buildings Grid */}
          <Grid container spacing={3}>
            {filteredBuildings.map((building) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={building.id}>
                <BuildingCard
                  building={building}
                  onEdit={handleEditBuilding}
                  onDelete={handleDeleteBuilding}
                  onView={handleViewBuilding}
                />
              </Grid>
            ))}
          </Grid>
        </>
      )}

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        aria-label="add building"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          display: { xs: 'flex', md: 'none' },
        }}
        onClick={handleCreateBuilding}
      >
        <AddIcon />
      </Fab>

      {/* Building Form Dialog */}
      <BuildingForm
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        building={editingBuilding}
        loading={formLoading}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{buildingToDelete?.name}"?
            This action will also delete all associated floors and apartments.
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default BuildingsPage;
