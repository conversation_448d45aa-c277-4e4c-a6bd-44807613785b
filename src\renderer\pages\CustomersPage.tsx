import React, { useState, useEffect, useMemo } from "react";
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  InputAdornment,
  Fab,
  Tooltip,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Chip,
  IconButton,
} from "@mui/material";
import {
  Add as AddIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  People as PeopleIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  CheckCircle as CheckCircleIcon,
  AttachMoney as AttachMoneyIcon,
  TrendingUp as TrendingUpIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import {
  CustomerWithCurrentApartment,
  CreateCustomerRequest,
  UpdateCustomerRequest,
} from "../../types/Customer";
import { CustomerService } from "../services/customerService";
import { useNotification } from "../contexts/NotificationContext";
import CustomerForm from "../components/forms/CustomerForm";
import CustomerCard from "../components/cards/CustomerCard";

const CustomersPage: React.FC = () => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  const [customers, setCustomers] = useState<CustomerWithCurrentApartment[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    "all" | "active" | "inactive"
  >("all");
  const [formOpen, setFormOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<
    CustomerWithCurrentApartment | undefined
  >();
  const [formLoading, setFormLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] =
    useState<CustomerWithCurrentApartment | null>(null);

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    try {
      setLoading(true);

      // Get customers from the database
      const customersResponse = await CustomerService.getAllCustomers();

      if (!customersResponse.success) {
        showError(customersResponse.error || "Failed to load customers");
        return;
      }

      const customersData = customersResponse.data || [];

      // Transform the database response to match our interface
      const transformedCustomers: CustomerWithCurrentApartment[] =
        customersData.map((cust: any) => ({
          id: cust.id,
          name: cust.name,
          address: cust.address,
          mobile: cust.mobile,
          occupation: cust.occupation,
          startDate: new Date(cust.start_date),
          rent: cust.rent,
          advance: cust.advance,
          nidPath: cust.nid_path,
          photoPath: cust.photo_path,
          isActive: cust.is_active === 1,
          createdAt: new Date(cust.created_at),
          updatedAt: new Date(cust.updated_at),
          currentApartment: cust.apartment_name
            ? {
                id: cust.apartment_id || 0,
                name: cust.apartment_name,
                currentRate: cust.rent,
                size: cust.apartment_size || "",
                numberOfRooms: cust.number_of_rooms || 0,
                floorId: cust.floor_id || 0,
                currentCustomerId: cust.id,
                isOccupied: true,
                createdAt: new Date(),
                updatedAt: new Date(),
                floor: {
                  id: cust.floor_id || 0,
                  name: cust.floor_name || "",
                  floorNumber: cust.floor_number || 0,
                  buildingId: cust.building_id || 0,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                },
                building: {
                  id: cust.building_id || 0,
                  name: cust.building_name || "Unknown Building",
                  address: cust.building_address || "Unknown Address",
                  ownerName: "Unknown Owner",
                  ownerMobileNo: "",
                  ownerAddress: "",
                  rentAmount: 0,
                  advance: 0,
                  numberOfFloor: 1,
                  agreementDate: new Date(),
                  handOverFromOwner: new Date(),
                  handOverToOwner: undefined,
                  conditions: "",
                  comments: "",
                  createdAt: new Date(),
                  updatedAt: new Date(),
                },
              }
            : undefined,
        }));

      setCustomers(CustomerService.sortCustomersByName(transformedCustomers));
    } catch (error) {
      showError("Failed to load customers");
      console.error("Error loading customers:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCustomer = () => {
    setEditingCustomer(undefined);
    setFormOpen(true);
  };

  const handleEditCustomer = (customer: CustomerWithCurrentApartment) => {
    setEditingCustomer(customer);
    setFormOpen(true);
  };

  const handleDeleteCustomer = (customer: CustomerWithCurrentApartment) => {
    setCustomerToDelete(customer);
    setDeleteDialogOpen(true);
  };

  const handleFormSubmit = async (
    data: CreateCustomerRequest | UpdateCustomerRequest
  ) => {
    try {
      setFormLoading(true);

      if ("id" in data) {
        const updateResponse = await CustomerService.updateCustomer(data);
        if (updateResponse.success) {
          showSuccess("Customer updated successfully");
          loadCustomers(); // Reload the list
        } else {
          showError(updateResponse.error || "Failed to update customer");
        }
      } else {
        const createResponse = await CustomerService.createCustomer(data);
        if (createResponse.success) {
          showSuccess("Customer created successfully");
          loadCustomers(); // Reload the list
        } else {
          showError(createResponse.error || "Failed to create customer");
        }
      }

      setFormOpen(false);
      setEditingCustomer(undefined);
    } catch (error) {
      showError("An unexpected error occurred");
      console.error("Error submitting customer form:", error);
    } finally {
      setFormLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (!customerToDelete) return;

    try {
      const deleteResponse = await CustomerService.deleteCustomer(
        customerToDelete.id
      );
      if (deleteResponse.success) {
        showSuccess("Customer deleted successfully");
        loadCustomers(); // Reload the list
      } else {
        showError(deleteResponse.error || "Failed to delete customer");
      }

      setDeleteDialogOpen(false);
      setCustomerToDelete(null);
    } catch (error) {
      showError("An unexpected error occurred");
      console.error("Error deleting customer:", error);
    }
  };

  const handleViewDetails = (customer: CustomerWithCurrentApartment) => {
    navigate(`/customers/${customer.id}`);
  };

  const handleSearch = (searchTerm: string) => {
    setSearchTerm(searchTerm);
  };

  const handleStatusFilter = (status: "all" | "active" | "inactive") => {
    setStatusFilter(status);
  };

  // Apply filters
  const filteredCustomers = useMemo(() => {
    let result = customers;

    // Apply search filter
    if (searchTerm) {
      result = CustomerService.filterCustomers(result, searchTerm);
    }

    // Apply status filter
    result = CustomerService.filterCustomersByStatus(result, statusFilter);

    return result;
  }, [customers, searchTerm, statusFilter]);

  const stats = useMemo(() => {
    return CustomerService.calculateCustomerStats(customers);
  }, [customers]);

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          Customers
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateCustomer}
        >
          Add Customer
        </Button>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <PeopleIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Customers
                  </Typography>
                  <Typography variant="h4">{stats.totalCustomers}</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <CheckCircleIcon color="success" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Active Customers
                  </Typography>
                  <Typography variant="h4">{stats.activeCustomers}</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <AttachMoneyIcon color="success" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Monthly Collection
                  </Typography>
                  <Typography variant="h4">
                    ${stats.totalRentCollection.toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color="info" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Average Rent
                  </Typography>
                  <Typography variant="h4">
                    ${Math.round(stats.averageRent).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Filter Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <SearchIcon sx={{ mr: 1, color: "text.secondary" }} />
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) =>
                    handleStatusFilter(
                      e.target.value as "all" | "active" | "inactive"
                    )
                  }
                >
                  <MenuItem value="all">All Customers</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="text.secondary">
                Showing {filteredCustomers.length} of {customers.length}{" "}
                customers
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Customers Table */}
      <Card>
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Mobile</TableCell>
                  <TableCell>Occupation</TableCell>
                  <TableCell>Current Apartment</TableCell>
                  <TableCell>Rent</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredCustomers.map((customer) => (
                  <TableRow key={customer.id} hover>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ mr: 2 }}>
                          {customer.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2">
                            {customer.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {customer.address}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>{customer.mobile}</TableCell>
                    <TableCell>{customer.occupation}</TableCell>
                    <TableCell>
                      {customer.currentApartment ? (
                        <Chip
                          label={customer.currentApartment.name}
                          color="primary"
                          size="small"
                        />
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          Not assigned
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2">
                        ${customer.rent.toLocaleString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={CustomerService.getCustomerStatusText(customer)}
                        color={customer.isActive ? "success" : "default"}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        onClick={() => handleViewDetails(customer)}
                        title="View Details"
                      >
                        <VisibilityIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleEditCustomer(customer)}
                        title="Edit"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteCustomer(customer)}
                        title="Delete"
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredCustomers.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography variant="body2" color="text.secondary" py={4}>
                        No customers found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Customer Form Dialog */}
      <CustomerForm
        open={formOpen}
        customer={editingCustomer}
        loading={formLoading}
        onClose={() => {
          setFormOpen(false);
          setEditingCustomer(undefined);
        }}
        onSubmit={handleFormSubmit}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete customer "{customerToDelete?.name}"?
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default CustomersPage;
