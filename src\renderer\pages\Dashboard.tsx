import React, { useState, useEffect } from "react";
import {
  Con<PERSON><PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Box,
  Button,
  CircularProgress,
} from "@mui/material";
import {
  Business as BusinessIcon,
  Apartment as ApartmentIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Add as AddIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useNotification } from "../contexts/NotificationContext";
import { DashboardService, DashboardStats } from "../services/dashboardService";

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { showError } = useNotification();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);

      const response = await DashboardService.getDashboardStats();

      if (!response.success) {
        showError(response.error || "Failed to load dashboard statistics");
        return;
      }

      setStats(response.data);
    } catch (error) {
      showError("Failed to load dashboard statistics");
      console.error("Error loading dashboard stats:", error);
    } finally {
      setLoading(false);
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
    subtitle?: string;
  }> = ({ title, value, icon, color, subtitle }) => (
    <Card sx={{ height: "100%" }}>
      <CardContent>
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <Box
            sx={{
              bgcolor: `${color}.light`,
              color: `${color}.main`,
              p: 1,
              borderRadius: 1,
              mr: 2,
            }}
          >
            {icon}
          </Box>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>
        </Box>
        <Typography
          variant="h4"
          component="div"
          sx={{ mb: 1, fontWeight: 600 }}
        >
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          sx={{ fontWeight: 600 }}
        >
          Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Overview of your apartment rental management system
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Buildings"
            value={stats?.totalBuildings || 0}
            icon={<BusinessIcon />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Apartments"
            value={stats?.totalApartments || 0}
            icon={<ApartmentIcon />}
            color="secondary"
            subtitle={`${stats?.occupiedApartments || 0} occupied`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Customers"
            value={stats?.totalCustomers || 0}
            icon={<PeopleIcon />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Monthly Revenue"
            value={`$${(stats?.monthlyRevenue || 0).toLocaleString()}`}
            icon={<MoneyIcon />}
            color="warning"
            subtitle={`${stats?.occupancyRate || 0}% occupancy rate`}
          />
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
            Quick Actions
          </Typography>
          <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate("/buildings")}
            >
              Add New Building
            </Button>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={() => navigate("/customers")}
            >
              Add New Customer
            </Button>
            <Button variant="outlined" onClick={() => navigate("/buildings")}>
              Manage Buildings
            </Button>
            <Button variant="outlined" onClick={() => navigate("/customers")}>
              Manage Customers
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Recent Activity - Placeholder */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
            Recent Activity
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Recent activity will be displayed here once the system is fully
            implemented.
          </Typography>
        </CardContent>
      </Card>
    </Container>
  );
};

export default Dashboard;
