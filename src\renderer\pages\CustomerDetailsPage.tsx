import React, { useState, useEffect } from "react";
import {
  Container,
  <PERSON>po<PERSON>,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Breadcrumbs,
  Link,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
} from "@mui/material";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  People as PeopleIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Work as WorkIcon,
  Home as HomeIcon,
  AttachMoney as MoneyIcon,
  CalendarToday as CalendarIcon,
  History as HistoryIcon,
  NavigateNext as NavigateNextIcon,
  CheckCircle as ActiveIcon,
  RadioButtonUnchecked as InactiveIcon,
  Assignment as DocumentIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
} from "@mui/icons-material";
import { useParams, useNavigate } from "react-router-dom";
import { CustomerWithCurrentApartment } from "../../types/Customer";
import { ApartmentBookingHistory } from "../../types/Apartment";
import { CustomerService } from "../services/customerService";
import { useNotification } from "../contexts/NotificationContext";

const CustomerDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  const [customer, setCustomer] = useState<CustomerWithCurrentApartment | null>(
    null
  );
  const [bookingHistory, setBookingHistory] = useState<
    ApartmentBookingHistory[]
  >([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadCustomerDetails();
    }
  }, [id]);

  const loadCustomerDetails = async () => {
    try {
      setLoading(true);

      // Get customer details from the database
      const customerResponse = await CustomerService.getCustomerById(parseInt(id!));

      if (!customerResponse.success) {
        showError(customerResponse.error || 'Failed to load customer details');
        return;
      }

      const custData = customerResponse.data;

      // Transform the database response to match our interface
      const transformedCustomer: CustomerWithCurrentApartment = {
        id: custData.id,
        name: custData.name,
        address: custData.address,
        mobile: custData.mobile,
        occupation: custData.occupation,
        startDate: new Date(custData.start_date),
        rent: custData.rent,
        advance: custData.advance,
        nidPath: custData.nid_path,
        photoPath: custData.photo_path,
        isActive: custData.is_active === 1,
        createdAt: new Date(custData.created_at),
        updatedAt: new Date(custData.updated_at),
        currentApartment: custData.apartment_name ? {
          id: custData.apartment_id || 0,
          name: custData.apartment_name,
          currentRate: custData.rent,
          size: custData.apartment_size || '',
          numberOfRooms: custData.number_of_rooms || 0,
          floorId: custData.floor_id || 0,
          currentCustomerId: custData.id,
          isOccupied: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          floor: {
            id: custData.floor_id || 0,
            name: custData.floor_name || '',
            floorNumber: custData.floor_number || 0,
            buildingId: custData.building_id || 0,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          building: {
            id: custData.building_id || 0,
            name: custData.building_name || 'Unknown Building',
            address: custData.building_address || 'Unknown Address',
            ownerName: 'Unknown Owner',
            ownerMobileNo: '',
            ownerAddress: '',
            rentAmount: 0,
            advance: 0,
            numberOfFloor: 1,
            agreementDate: new Date(),
            handOverFromOwner: new Date(),
            handOverToOwner: undefined,
            conditions: '',
            comments: '',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        } : undefined,
      };

      // Get customer booking history from the database
      const historyResponse = await CustomerService.getCustomerHistory(parseInt(id!));

      let transformedHistory: ApartmentBookingHistory[] = [];
      if (historyResponse.success && historyResponse.data) {
        transformedHistory = historyResponse.data.map((booking: any) => ({
          id: booking.id,
          apartmentId: booking.apartment_id,
          customerId: booking.customer_id,
          customer: transformedCustomer,
          startDate: new Date(booking.start_date),
          endDate: booking.end_date ? new Date(booking.end_date) : undefined,
          rentAmount: booking.rent_amount,
          advanceAmount: booking.advance_amount,
          checkoutReason: booking.checkout_reason,
          createdAt: new Date(booking.created_at),
          updatedAt: new Date(booking.updated_at),
        }));
      }

      setCustomer(transformedCustomer);
      setBookingHistory(transformedHistory);
    } catch (error) {
      showError("Failed to load customer details");
      console.error("Error loading customer details:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // TODO: Implement edit functionality
    showSuccess("Edit functionality will be implemented");
  };

  const handleDelete = () => {
    // TODO: Implement delete functionality
    showSuccess("Delete functionality will be implemented");
  };

  const handleViewDocument = (documentPath: string) => {
    // TODO: Implement document viewing
    window.open(documentPath, "_blank");
  };

  const handleDownloadDocument = (documentPath: string, fileName: string) => {
    // TODO: Implement document download
    showSuccess(`Download functionality for ${fileName} will be implemented`);
  };

  const formatDate = (date: Date): string => {
    return CustomerService.formatDate(date);
  };

  const formatCurrency = (amount: number): string => {
    return CustomerService.formatCurrency(amount);
  };

  if (!id) {
    return (
      <Container maxWidth="xl">
        <Alert severity="error">Customer ID is required</Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ display: "flex", justifyContent: "center", py: 8 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!customer) {
    return (
      <Container maxWidth="xl">
        <Alert severity="error">Customer not found</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      {/* Breadcrumbs */}
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />}>
          <Link
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigate("/customers");
            }}
            sx={{
              display: "flex",
              alignItems: "center",
              textDecoration: "none",
            }}
          >
            <PeopleIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Customers
          </Link>
          <Typography
            color="text.primary"
            sx={{ display: "flex", alignItems: "center" }}
          >
            <PersonIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            {customer.name}
          </Typography>
        </Breadcrumbs>
      </Box>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-start",
            mb: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Avatar sx={{ width: 64, height: 64 }} src={customer.photoPath}>
              {!customer.photoPath && (
                <Typography variant="h4" sx={{ fontWeight: 600 }}>
                  {customer.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                    .toUpperCase()}
                </Typography>
              )}
            </Avatar>
            <Box>
              <Typography
                variant="h4"
                component="h1"
                gutterBottom
                sx={{ fontWeight: 600 }}
              >
                {customer.name}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {customer.occupation}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={handleEdit}
            >
              Edit
            </Button>
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDelete}
            >
              Delete
            </Button>
          </Box>
        </Box>

        {/* Status Chip */}
        <Chip
          icon={customer.isActive ? <ActiveIcon /> : <InactiveIcon />}
          label={CustomerService.getCustomerStatusText(customer)}
          color={customer.isActive ? "success" : "default"}
          size="medium"
          sx={{ fontWeight: 600 }}
        />
      </Box>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Personal Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Personal Information
              </Typography>
              <List>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: "primary.main" }}>
                      <PersonIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText primary="Full Name" secondary={customer.name} />
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: "secondary.main" }}>
                      <PhoneIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Mobile Number"
                    secondary={customer.mobile}
                  />
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: "info.main" }}>
                      <WorkIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Occupation"
                    secondary={customer.occupation}
                  />
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: "warning.main" }}>
                      <HomeIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Address"
                    secondary={customer.address}
                  />
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: "success.main" }}>
                      <CalendarIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Start Date"
                    secondary={formatDate(customer.startDate)}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Financial Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Financial Information
              </Typography>
              <List>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: "success.main" }}>
                      <MoneyIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Monthly Rent"
                    secondary={formatCurrency(customer.rent)}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Advance Amount"
                    secondary={formatCurrency(customer.advance)}
                    sx={{ pl: 7 }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Total Paid"
                    secondary={formatCurrency(customer.rent + customer.advance)}
                    sx={{ pl: 7 }}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Current Apartment Information */}
      {customer.isActive && customer.currentApartment && (
        <Box sx={{ mt: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Current Apartment
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <List>
                    <ListItem>
                      <ListItemText
                        primary="Apartment"
                        secondary={customer.currentApartment.name}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Floor"
                        secondary={customer.currentApartment.floor?.name}
                      />
                    </ListItem>
                  </List>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <List>
                    <ListItem>
                      <ListItemText
                        primary="Building"
                        secondary={customer.currentApartment.building?.name}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Size & Rooms"
                        secondary={`${customer.currentApartment.size} • ${customer.currentApartment.numberOfRooms} rooms`}
                      />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Documents Section */}
      <Box sx={{ mt: 4 }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
              Documents
            </Typography>
            <Grid container spacing={2}>
              {customer.nidPath && (
                <Grid item xs={12} sm={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: "center" }}>
                    <DocumentIcon
                      sx={{ fontSize: 48, color: "primary.main", mb: 1 }}
                    />
                    <Typography variant="subtitle1" gutterBottom>
                      NID Document
                    </Typography>
                    <Box
                      sx={{ display: "flex", justifyContent: "center", gap: 1 }}
                    >
                      <IconButton
                        size="small"
                        onClick={() => handleViewDocument(customer.nidPath!)}
                        color="primary"
                      >
                        <ViewIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() =>
                          handleDownloadDocument(customer.nidPath!, "NID.jpg")
                        }
                        color="secondary"
                      >
                        <DownloadIcon />
                      </IconButton>
                    </Box>
                  </Paper>
                </Grid>
              )}

              {customer.photoPath && (
                <Grid item xs={12} sm={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: "center" }}>
                    <Avatar
                      src={customer.photoPath}
                      sx={{ width: 64, height: 64, mx: "auto", mb: 1 }}
                    />
                    <Typography variant="subtitle1" gutterBottom>
                      Customer Photo
                    </Typography>
                    <Box
                      sx={{ display: "flex", justifyContent: "center", gap: 1 }}
                    >
                      <IconButton
                        size="small"
                        onClick={() => handleViewDocument(customer.photoPath!)}
                        color="primary"
                      >
                        <ViewIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() =>
                          handleDownloadDocument(
                            customer.photoPath!,
                            "Photo.jpg"
                          )
                        }
                        color="secondary"
                      >
                        <DownloadIcon />
                      </IconButton>
                    </Box>
                  </Paper>
                </Grid>
              )}

              {!customer.nidPath && !customer.photoPath && (
                <Grid item xs={12}>
                  <Alert severity="info">
                    No documents uploaded for this customer.
                  </Alert>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      </Box>

      {/* Booking History */}
      <Box sx={{ mt: 4 }}>
        <Card>
          <CardContent>
            <Typography
              variant="h6"
              gutterBottom
              sx={{ fontWeight: 600, display: "flex", alignItems: "center" }}
            >
              <HistoryIcon sx={{ mr: 1 }} />
              Rental History
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {bookingHistory.length > 0 ? (
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Apartment</TableCell>
                      <TableCell>Start Date</TableCell>
                      <TableCell>End Date</TableCell>
                      <TableCell>Rent Amount</TableCell>
                      <TableCell>Advance</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Checkout Reason</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {bookingHistory.map((booking) => (
                      <TableRow key={booking.id}>
                        <TableCell>
                          <Box>
                            <Typography
                              variant="body2"
                              sx={{ fontWeight: 600 }}
                            >
                              {booking.apartment.name}
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              {booking.apartment.floor?.name} •{" "}
                              {booking.apartment.building?.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{formatDate(booking.startDate)}</TableCell>
                        <TableCell>
                          {booking.endDate ? (
                            formatDate(booking.endDate)
                          ) : (
                            <Chip
                              label="Current"
                              color="success"
                              size="small"
                            />
                          )}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(booking.rentAmount)}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(booking.advanceAmount)}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={booking.endDate ? "Completed" : "Active"}
                            color={booking.endDate ? "default" : "success"}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {booking.checkoutReason ||
                            (booking.endDate ? "N/A" : "-")}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">
                No rental history available for this customer.
              </Alert>
            )}
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default CustomerDetailsPage;
