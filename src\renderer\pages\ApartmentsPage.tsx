import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  InputAdornment,
  Fab,
  Tooltip,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Business as BusinessIcon,
  Layers as LayersIcon,
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { ApartmentWithDetails, CreateApartmentRequest, UpdateApartmentRequest } from '../../types/Apartment';
import { Floor } from '../../types/Floor';
import { Building } from '../../types/Building';
import { ApartmentService } from '../services/apartmentService';
import { useNotification } from '../contexts/NotificationContext';
import ApartmentForm from '../components/forms/ApartmentForm';
import ApartmentCard from '../components/cards/ApartmentCard';

const ApartmentsPage: React.FC = () => {
  const { floorId } = useParams<{ floorId: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  const [apartments, setApartments] = useState<ApartmentWithDetails[]>([]);
  const [floor, setFloor] = useState<Floor | null>(null);
  const [building, setBuilding] = useState<Building | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'occupied' | 'available'>('all');
  const [formOpen, setFormOpen] = useState(false);
  const [editingApartment, setEditingApartment] = useState<ApartmentWithDetails | undefined>();
  const [formLoading, setFormLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [apartmentToDelete, setApartmentToDelete] = useState<ApartmentWithDetails | null>(null);

  useEffect(() => {
    if (floorId) {
      loadFloorAndApartments();
    }
  }, [floorId]);

  const loadFloorAndApartments = async () => {
    try {
      setLoading(true);

      // Get apartments for this floor from the database
      const apartmentsResponse = await ApartmentService.getApartmentsByFloor(parseInt(floorId!));

      if (!apartmentsResponse.success) {
        showError(apartmentsResponse.error || 'Failed to load apartments');
        return;
      }

      const apartmentsData = apartmentsResponse.data || [];

      // Transform the database response to match our interface
      const transformedApartments: ApartmentWithDetails[] = apartmentsData.map((apt: any) => ({
        id: apt.id,
        name: apt.name,
        currentRate: apt.current_rate,
        size: apt.size,
        numberOfRooms: apt.number_of_rooms,
        floorId: apt.floor_id,
        currentCustomerId: apt.current_customer_id,
        isOccupied: apt.is_occupied === 1,
        createdAt: new Date(apt.created_at),
        updatedAt: new Date(apt.updated_at),
        floor: {
          id: apt.floor_id,
          name: apt.floor_name,
          floorNumber: apt.floor_number,
          buildingId: apt.building_id || 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        building: {
          id: apt.building_id || 1,
          name: apt.building_name || 'Unknown Building',
          address: apt.building_address || 'Unknown Address',
          ownerName: 'Unknown Owner',
          ownerMobileNo: '',
          ownerAddress: '',
          rentAmount: 0,
          advance: 0,
          numberOfFloor: 1,
          agreementDate: new Date(),
          handOverFromOwner: new Date(),
          handOverToOwner: undefined,
          conditions: '',
          comments: '',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        currentCustomer: apt.customer_name ? {
          id: apt.current_customer_id,
          name: apt.customer_name,
          address: apt.customer_address || '',
          mobile: apt.customer_mobile || '',
          occupation: apt.customer_occupation || '',
          startDate: new Date(apt.customer_start_date || Date.now()),
          rent: apt.customer_rent || apt.current_rate,
          advance: apt.customer_advance || 0,
          isActive: apt.customer_is_active === 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        } : undefined,
      }));

      // Set floor and building info from the first apartment (if any)
      if (transformedApartments.length > 0) {
        setFloor(transformedApartments[0].floor);
        setBuilding(transformedApartments[0].building);
      } else {
        // If no apartments, we still need floor info - this should be improved to fetch floor details separately
        setFloor({
          id: parseInt(floorId!),
          name: `Floor ${floorId}`,
          floorNumber: parseInt(floorId!) - 1,
          buildingId: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      setApartments(ApartmentService.sortApartmentsByName(transformedApartments));
    } catch (error) {
      showError('Failed to load floor and apartments');
      console.error('Error loading floor and apartments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateApartment = () => {
    setEditingApartment(undefined);
    setFormOpen(true);
  };

  const handleEditApartment = (apartment: ApartmentWithDetails) => {
    setEditingApartment(apartment);
    setFormOpen(true);
  };

  const handleDeleteApartment = (apartment: ApartmentWithDetails) => {
    setApartmentToDelete(apartment);
    setDeleteDialogOpen(true);
  };

  const handleViewApartment = (apartment: ApartmentWithDetails) => {
    console.log('Viewing apartment:', apartment.name);
  };

  const handleBookApartment = (apartment: ApartmentWithDetails) => {
    // TODO: Implement booking functionality
    showSuccess(`Booking functionality for ${apartment.name} will be implemented`);
  };

  const handleCheckoutApartment = (apartment: ApartmentWithDetails) => {
    // TODO: Implement checkout functionality
    showSuccess(`Checkout functionality for ${apartment.name} will be implemented`);
  };

  const handleFormSubmit = async (data: CreateApartmentRequest | UpdateApartmentRequest) => {
    try {
      setFormLoading(true);

      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      if ('id' in data) {
        showSuccess('Apartment updated successfully');
      } else {
        showSuccess('Apartment created successfully');
      }

      setFormOpen(false);
      await loadFloorAndApartments();
    } catch (error) {
      showError('Failed to save apartment');
      console.error('Error saving apartment:', error);
    } finally {
      setFormLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (!apartmentToDelete) return;

    try {
      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      showSuccess('Apartment deleted successfully');
      setDeleteDialogOpen(false);
      setApartmentToDelete(null);
      await loadFloorAndApartments();
    } catch (error) {
      showError('Failed to delete apartment');
      console.error('Error deleting apartment:', error);
    }
  };

  const filteredApartments = ApartmentService.filterApartmentsByStatus(
    ApartmentService.filterApartments(apartments, searchTerm),
    statusFilter
  );

  const apartmentStats = ApartmentService.calculateApartmentStats(apartments);

  if (!floorId) {
    return (
      <Container maxWidth="xl">
        <Alert severity="error">Floor ID is required</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      {/* Breadcrumbs */}
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />}>
          <Link
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigate('/buildings');
            }}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Buildings
          </Link>
          <Link
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigate(`/buildings/${building?.id}/floors`);
            }}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <LayersIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            {building?.name || 'Floors'}
          </Link>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            {floor?.name || 'Apartments'}
          </Typography>
        </Breadcrumbs>
      </Box>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
              Apartments Management
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {floor && building ? `Manage apartments for ${floor.name} in ${building.name}` : 'Loading apartment information...'}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Tooltip title="Refresh">
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadFloorAndApartments}
                disabled={loading}
              >
                Refresh
              </Button>
            </Tooltip>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateApartment}
              size="large"
            >
              Add Apartment
            </Button>
          </Box>
        </Box>

        {/* Floor Info Card */}
        {floor && building && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {floor.name} - {building.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {building.address}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip label={`${apartmentStats.totalApartments} Apartments`} color="primary" />
                  <Chip label={`${apartmentStats.occupiedApartments} Occupied`} color="success" />
                  <Chip label={`${apartmentStats.availableApartments} Available`} color="default" />
                  <Chip
                    label={`${apartmentStats.occupancyRate.toFixed(1)}% Occupied`}
                    color={apartmentStats.occupancyRate >= 80 ? 'success' : apartmentStats.occupancyRate >= 50 ? 'warning' : 'error'}
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        )}

        {/* Search and Filter */}
        <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
          <TextField
            placeholder="Search apartments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300, flexGrow: 1 }}
          />

          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Status Filter</InputLabel>
            <Select
              value={statusFilter}
              label="Status Filter"
              onChange={(e) => setStatusFilter(e.target.value as 'all' | 'occupied' | 'available')}
              startAdornment={<FilterIcon sx={{ mr: 1 }} />}
            >
              <MenuItem value="all">All Apartments</MenuItem>
              <MenuItem value="occupied">Occupied Only</MenuItem>
              <MenuItem value="available">Available Only</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      ) : apartments.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          No apartments found for this floor. Click "Add Apartment" to create apartments.
        </Alert>
      ) : (
        <>
          {/* Statistics */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Apartment Statistics
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary" sx={{ fontWeight: 600 }}>
                      {apartmentStats.totalApartments}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Apartments
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                      {apartmentStats.occupiedApartments}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Occupied
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                      {apartmentStats.availableApartments}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Available
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="secondary.main" sx={{ fontWeight: 600 }}>
                      {ApartmentService.formatCurrency(apartmentStats.totalRentCollection)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Monthly Collection
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>

          {/* Apartments Grid */}
          <Grid container spacing={3}>
            {filteredApartments.map((apartment) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={apartment.id}>
                <ApartmentCard
                  apartment={apartment}
                  onEdit={handleEditApartment}
                  onDelete={handleDeleteApartment}
                  onView={handleViewApartment}
                  onBook={handleBookApartment}
                  onCheckout={handleCheckoutApartment}
                />
              </Grid>
            ))}
          </Grid>

          {/* No Results Message */}
          {filteredApartments.length === 0 && (
            <Alert severity="info" sx={{ mt: 3 }}>
              No apartments match your current search and filter criteria.
            </Alert>
          )}
        </>
      )}

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        aria-label="add apartment"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          display: { xs: 'flex', md: 'none' },
        }}
        onClick={handleCreateApartment}
      >
        <AddIcon />
      </Fab>

      {/* Apartment Form Dialog */}
      <ApartmentForm
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        apartment={editingApartment}
        floorId={parseInt(floorId)}
        floorNumber={floor?.floorNumber || 0}
        loading={formLoading}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Apartment</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete apartment "{apartmentToDelete?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ApartmentsPage;
