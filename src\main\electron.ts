import { app, BrowserWindow, ipc<PERSON>ain, dialog, shell } from "electron";
import { join } from "path";
import * as path from "path";
import { existsSync, mkdirSync } from "fs";
import Database from "better-sqlite3";
import { DatabaseService } from "./services/DatabaseService";
import { FileService } from "./services/FileService";
import { GoogleDriveService } from "./services/GoogleDriveService";
import { IPC_CHANNELS } from "../types/common";

class ElectronApp {
  private mainWindow: BrowserWindow | null = null;
  private databaseService: DatabaseService | null = null;
  private fileService: FileService | null = null;
  private googleDriveService: GoogleDriveService | null = null;

  constructor() {
    this.initializeApp();
  }

  private async initializeApp(): Promise<void> {
    // Handle app events
    app.whenReady().then(() => {
      this.createWindow();
      this.initializeServices();
      this.setupIpcHandlers();
    });

    app.on("window-all-closed", () => {
      if (process.platform !== "darwin") {
        app.quit();
      }
    });

    app.on("activate", () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });
  }

  private createWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 800,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: process.env.NODE_ENV !== "development",
        preload: path.resolve(__dirname, "preload.js"),
      },
      icon: join(__dirname, "../../public/icon.ico"),
      show: false, // Don't show until ready
      titleBarStyle: "default",
    });

    // Load the app
    console.log("NODE_ENV:", process.env.NODE_ENV);
    console.log("Preload script path:", path.resolve(__dirname, "preload.js"));

    if (process.env.NODE_ENV === "development") {
      console.log("Loading development URL: http://localhost:3000");
      this.mainWindow.loadURL("http://localhost:3000");
      this.mainWindow.webContents.openDevTools();
    } else {
      console.log("Loading production file");
      this.mainWindow.loadFile(join(__dirname, "../renderer/index.html"));
    }

    // Show window when ready
    this.mainWindow.once("ready-to-show", () => {
      this.mainWindow?.show();
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: "deny" };
    });
  }

  private async initializeServices(): Promise<void> {
    try {
      // Ensure data directories exist
      const userDataPath = app.getPath("userData");
      const dbPath = join(userDataPath, "database");
      const filesPath = join(userDataPath, "files");

      if (!existsSync(dbPath)) mkdirSync(dbPath, { recursive: true });
      if (!existsSync(filesPath)) mkdirSync(filesPath, { recursive: true });

      // Initialize database
      const dbFile = join(dbPath, "apartment_rental.db");
      const database = new Database(dbFile);
      this.databaseService = new DatabaseService(database);
      await this.databaseService.initialize();

      // Initialize file service
      this.fileService = new FileService(filesPath);

      // Initialize Google Drive service
      this.googleDriveService = new GoogleDriveService();

      console.log("All services initialized successfully");
    } catch (error) {
      console.error("Failed to initialize services:", error);
      dialog.showErrorBox(
        "Initialization Error",
        "Failed to initialize application services. Please restart the application."
      );
    }
  }

  private setupIpcHandlers(): void {
    // Database operations
    ipcMain.handle(IPC_CHANNELS.DB_INIT, async () => {
      return this.databaseService?.initialize();
    });

    ipcMain.handle(
      IPC_CHANNELS.DB_QUERY,
      async (_, sql: string, params?: any[]) => {
        return this.databaseService?.query(sql, params);
      }
    );

    ipcMain.handle(
      IPC_CHANNELS.DB_EXECUTE,
      async (_, sql: string, params?: any[]) => {
        return this.databaseService?.execute(sql, params);
      }
    );

    // File operations
    ipcMain.handle(
      IPC_CHANNELS.FILE_UPLOAD,
      async (
        _,
        fileData: Buffer,
        fileName: string,
        entityType: string,
        entityId: number,
        fieldName: string
      ) => {
        return this.fileService?.uploadFile(
          fileData,
          fileName,
          entityType,
          entityId,
          fieldName
        );
      }
    );

    ipcMain.handle(IPC_CHANNELS.FILE_DELETE, async (_, filePath: string) => {
      return this.fileService?.deleteFile(filePath);
    });

    ipcMain.handle(
      IPC_CHANNELS.FILE_GENERATE_THUMBNAIL,
      async (_, filePath: string) => {
        return this.fileService?.generateThumbnail(filePath);
      }
    );

    // Building operations
    ipcMain.handle(IPC_CHANNELS.BUILDING_CREATE, async (_, data) => {
      try {
        const sql = `
          INSERT INTO buildings (
            name, address, owner_name, owner_mobile_no, owner_address,
            rent_amount, advance, number_of_floor, agreement_date,
            hand_over_from_owner, hand_over_to_owner, conditions, comments
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        const result = this.databaseService?.execute(sql, [
          data.name,
          data.address,
          data.ownerName,
          data.ownerMobileNo,
          data.ownerAddress,
          data.rentAmount,
          data.advance,
          data.numberOfFloor,
          data.agreementDate,
          data.handOverFromOwner,
          data.handOverToOwner,
          data.conditions,
          data.comments,
        ]);
        return {
          success: true,
          data: { id: result?.lastInsertRowid, ...data },
        };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to create building",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.BUILDING_GET_ALL, async () => {
      try {
        const sql = `SELECT * FROM buildings ORDER BY created_at DESC`;
        const buildings = this.databaseService?.query(sql) || [];
        return { success: true, data: buildings };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to fetch buildings",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.BUILDING_GET_WITH_STATS, async () => {
      try {
        const sql = `
          SELECT b.*,
                 COUNT(DISTINCT f.id) as total_floors,
                 COUNT(DISTINCT a.id) as total_apartments,
                 COUNT(DISTINCT CASE WHEN a.current_customer_id IS NOT NULL THEN a.id END) as occupied_apartments,
                 COUNT(DISTINCT CASE WHEN a.current_customer_id IS NULL THEN a.id END) as available_apartments,
                 COALESCE(SUM(CASE WHEN a.current_customer_id IS NOT NULL THEN a.current_rate ELSE 0 END), 0) as total_rent_collection
          FROM buildings b
          LEFT JOIN floors f ON b.id = f.building_id
          LEFT JOIN apartments a ON f.id = a.floor_id
          GROUP BY b.id
          ORDER BY b.created_at DESC
        `;
        const buildings = this.databaseService?.query(sql) || [];
        return { success: true, data: buildings };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to fetch buildings with statistics",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.BUILDING_GET_BY_ID, async (_, id) => {
      try {
        const sql = `SELECT * FROM buildings WHERE id = ?`;
        const building = this.databaseService?.query(sql, [id])?.[0];
        if (!building) {
          return { success: false, error: "Building not found" };
        }
        return { success: true, data: building };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Failed to fetch building",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.BUILDING_UPDATE, async (_, data) => {
      try {
        const sql = `
          UPDATE buildings SET
            name = ?, address = ?, owner_name = ?, owner_mobile_no = ?,
            owner_address = ?, rent_amount = ?, advance = ?, number_of_floor = ?,
            agreement_date = ?, hand_over_from_owner = ?, hand_over_to_owner = ?,
            conditions = ?, comments = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
        this.databaseService?.execute(sql, [
          data.name,
          data.address,
          data.ownerName,
          data.ownerMobileNo,
          data.ownerAddress,
          data.rentAmount,
          data.advance,
          data.numberOfFloor,
          data.agreementDate,
          data.handOverFromOwner,
          data.handOverToOwner,
          data.conditions,
          data.comments,
          data.id,
        ]);
        return { success: true, data };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to update building",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.BUILDING_DELETE, async (_, id) => {
      try {
        const sql = `DELETE FROM buildings WHERE id = ?`;
        this.databaseService?.execute(sql, [id]);
        return { success: true };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to delete building",
        };
      }
    });

    // Floor operations
    ipcMain.handle(IPC_CHANNELS.FLOOR_CREATE, async (_, data) => {
      try {
        const sql = `
          INSERT INTO floors (name, floor_number, building_id)
          VALUES (?, ?, ?)
        `;
        const result = this.databaseService?.execute(sql, [
          data.name,
          data.floorNumber,
          data.buildingId,
        ]);
        return {
          success: true,
          data: { id: result?.lastInsertRowid, ...data },
        };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Failed to create floor",
        };
      }
    });

    ipcMain.handle(
      IPC_CHANNELS.FLOOR_GET_BY_BUILDING,
      async (_, buildingId) => {
        try {
          const sql = `
          SELECT f.*,
                 COUNT(a.id) as total_apartments,
                 COUNT(CASE WHEN a.current_customer_id IS NOT NULL THEN 1 END) as occupied_apartments,
                 COUNT(CASE WHEN a.current_customer_id IS NULL THEN 1 END) as available_apartments
          FROM floors f
          LEFT JOIN apartments a ON f.id = a.floor_id
          WHERE f.building_id = ?
          GROUP BY f.id
          ORDER BY f.floor_number
        `;
          const floors = this.databaseService?.query(sql, [buildingId]) || [];
          return { success: true, data: floors };
        } catch (error) {
          return {
            success: false,
            error:
              error instanceof Error ? error.message : "Failed to fetch floors",
          };
        }
      }
    );

    ipcMain.handle(IPC_CHANNELS.FLOOR_UPDATE, async (_, data) => {
      try {
        const sql = `
          UPDATE floors SET name = ?, floor_number = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
        this.databaseService?.execute(sql, [
          data.name,
          data.floorNumber,
          data.id,
        ]);
        return { success: true, data };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Failed to update floor",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.FLOOR_DELETE, async (_, id) => {
      try {
        const sql = `DELETE FROM floors WHERE id = ?`;
        this.databaseService?.execute(sql, [id]);
        return { success: true };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Failed to delete floor",
        };
      }
    });

    ipcMain.handle(
      IPC_CHANNELS.FLOOR_AUTO_GENERATE,
      async (_, buildingId, numberOfFloors) => {
        try {
          const floors = [];
          for (let i = 0; i < numberOfFloors; i++) {
            const name =
              i === 0
                ? "Ground Floor"
                : `${i}${
                    i === 1 ? "st" : i === 2 ? "nd" : i === 3 ? "rd" : "th"
                  } Floor`;
            const sql = `INSERT INTO floors (name, floor_number, building_id) VALUES (?, ?, ?)`;
            const result = this.databaseService?.execute(sql, [
              name,
              i,
              buildingId,
            ]);
            floors.push({
              id: result?.lastInsertRowid,
              name,
              floorNumber: i,
              buildingId,
            });
          }
          return { success: true, data: floors };
        } catch (error) {
          return {
            success: false,
            error:
              error instanceof Error
                ? error.message
                : "Failed to auto-generate floors",
          };
        }
      }
    );

    // Apartment operations
    ipcMain.handle(IPC_CHANNELS.APARTMENT_CREATE, async (_, data) => {
      try {
        const sql = `
          INSERT INTO apartments (name, current_rate, size, number_of_rooms, floor_id, is_occupied)
          VALUES (?, ?, ?, ?, ?, 0)
        `;
        const result = this.databaseService?.execute(sql, [
          data.name,
          data.currentRate,
          data.size,
          data.numberOfRooms,
          data.floorId,
        ]);
        return {
          success: true,
          data: { id: result?.lastInsertRowid, ...data },
        };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to create apartment",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.APARTMENT_GET_BY_FLOOR, async (_, floorId) => {
      try {
        const sql = `
          SELECT a.*,
                 f.name as floor_name, f.floor_number,
                 b.name as building_name, b.address as building_address,
                 c.name as customer_name, c.mobile as customer_mobile,
                 CASE WHEN a.current_customer_id IS NOT NULL THEN 1 ELSE 0 END as is_occupied
          FROM apartments a
          JOIN floors f ON a.floor_id = f.id
          JOIN buildings b ON f.building_id = b.id
          LEFT JOIN customers c ON a.current_customer_id = c.id
          WHERE a.floor_id = ?
          ORDER BY a.name
        `;
        const apartments = this.databaseService?.query(sql, [floorId]) || [];
        return { success: true, data: apartments };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to fetch apartments",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.APARTMENT_UPDATE, async (_, data) => {
      try {
        const sql = `
          UPDATE apartments SET
            name = ?, current_rate = ?, size = ?, number_of_rooms = ?,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
        this.databaseService?.execute(sql, [
          data.name,
          data.currentRate,
          data.size,
          data.numberOfRooms,
          data.id,
        ]);
        return { success: true, data };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to update apartment",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.APARTMENT_DELETE, async (_, id) => {
      try {
        const sql = `DELETE FROM apartments WHERE id = ?`;
        this.databaseService?.execute(sql, [id]);
        return { success: true };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to delete apartment",
        };
      }
    });

    // Get apartment with details
    ipcMain.handle(IPC_CHANNELS.APARTMENT_GET_WITH_DETAILS, async (_, id) => {
      try {
        const sql = `
          SELECT a.*,
                 f.name as floor_name, f.floor_number,
                 b.name as building_name, b.address as building_address,
                 c.name as customer_name, c.mobile as customer_mobile, c.address as customer_address,
                 c.occupation as customer_occupation, c.start_date as customer_start_date,
                 c.rent as customer_rent, c.advance as customer_advance, c.is_active as customer_is_active,
                 CASE WHEN a.current_customer_id IS NOT NULL THEN 1 ELSE 0 END as is_occupied
          FROM apartments a
          JOIN floors f ON a.floor_id = f.id
          JOIN buildings b ON f.building_id = b.id
          LEFT JOIN customers c ON a.current_customer_id = c.id
          WHERE a.id = ?
        `;
        const apartment = this.databaseService?.query(sql, [id])?.[0];
        if (!apartment) {
          return { success: false, error: "Apartment not found" };
        }
        return { success: true, data: apartment };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to fetch apartment details",
        };
      }
    });

    // Book apartment
    ipcMain.handle(IPC_CHANNELS.APARTMENT_BOOK, async (_, data) => {
      try {
        // Start transaction
        this.databaseService?.execute("BEGIN TRANSACTION");

        // Update apartment with customer
        const updateApartmentSql = `
          UPDATE apartments SET
            current_customer_id = ?,
            is_occupied = 1,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ? AND current_customer_id IS NULL
        `;
        const result = this.databaseService?.execute(updateApartmentSql, [
          data.customerId,
          data.apartmentId,
        ]);

        if (result?.changes === 0) {
          this.databaseService?.execute("ROLLBACK");
          return {
            success: false,
            error: "Apartment is already occupied or not found",
          };
        }

        // Update customer as active
        const updateCustomerSql = `
          UPDATE customers SET
            is_active = 1,
            start_date = ?,
            rent = ?,
            advance = ?,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
        this.databaseService?.execute(updateCustomerSql, [
          data.startDate,
          data.rentAmount,
          data.advanceAmount,
          data.customerId,
        ]);

        // Create booking history record
        const historySQL = `
          INSERT INTO apartment_bookings
          (apartment_id, customer_id, start_date, rent_amount, advance_amount)
          VALUES (?, ?, ?, ?, ?)
        `;
        const historyResult = this.databaseService?.execute(historySQL, [
          data.apartmentId,
          data.customerId,
          data.startDate,
          data.rentAmount,
          data.advanceAmount,
        ]);

        this.databaseService?.execute("COMMIT");
        return {
          success: true,
          data: { id: historyResult?.lastInsertRowid, ...data },
        };
      } catch (error) {
        this.databaseService?.execute("ROLLBACK");
        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Failed to book apartment",
        };
      }
    });

    // Checkout apartment
    ipcMain.handle(IPC_CHANNELS.APARTMENT_CHECKOUT, async (_, data) => {
      try {
        // Start transaction
        this.databaseService?.execute("BEGIN TRANSACTION");

        // Get current booking
        const getCurrentBookingSql = `
          SELECT * FROM apartment_bookings
          WHERE apartment_id = ? AND end_date IS NULL
          ORDER BY created_at DESC LIMIT 1
        `;
        const currentBooking = this.databaseService?.query(
          getCurrentBookingSql,
          [data.apartmentId]
        )?.[0];

        if (!currentBooking) {
          this.databaseService?.execute("ROLLBACK");
          return {
            success: false,
            error: "No active booking found for this apartment",
          };
        }

        // Update booking history with end date
        const updateHistorySql = `
          UPDATE apartment_bookings SET
            end_date = ?,
            checkout_reason = ?,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
        this.databaseService?.execute(updateHistorySql, [
          data.endDate,
          data.checkoutReason,
          currentBooking.id,
        ]);

        // Update apartment to remove customer
        const updateApartmentSql = `
          UPDATE apartments SET
            current_customer_id = NULL,
            is_occupied = 0,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
        this.databaseService?.execute(updateApartmentSql, [data.apartmentId]);

        // Update customer as inactive
        const updateCustomerSql = `
          UPDATE customers SET
            is_active = 0,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
        this.databaseService?.execute(updateCustomerSql, [
          currentBooking.customer_id,
        ]);

        this.databaseService?.execute("COMMIT");
        return {
          success: true,
          data: {
            ...currentBooking,
            end_date: data.endDate,
            checkout_reason: data.checkoutReason,
          },
        };
      } catch (error) {
        this.databaseService?.execute("ROLLBACK");
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to checkout apartment",
        };
      }
    });

    // Get apartment booking history
    ipcMain.handle(
      IPC_CHANNELS.APARTMENT_GET_HISTORY,
      async (_, apartmentId) => {
        try {
          const sql = `
          SELECT ab.*,
                 c.name as customer_name, c.mobile as customer_mobile, c.address as customer_address,
                 c.occupation as customer_occupation
          FROM apartment_bookings ab
          JOIN customers c ON ab.customer_id = c.id
          WHERE ab.apartment_id = ?
          ORDER BY ab.created_at DESC
        `;
          const history = this.databaseService?.query(sql, [apartmentId]) || [];
          return { success: true, data: history };
        } catch (error) {
          return {
            success: false,
            error:
              error instanceof Error
                ? error.message
                : "Failed to fetch apartment history",
          };
        }
      }
    );

    // Customer operations
    ipcMain.handle(IPC_CHANNELS.CUSTOMER_CREATE, async (_, data) => {
      try {
        const sql = `
          INSERT INTO customers (
            name, address, mobile, occupation, start_date, rent, advance,
            nid_path, photo_path, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        const result = this.databaseService?.execute(sql, [
          data.name,
          data.address,
          data.mobile,
          data.occupation,
          data.startDate,
          data.rent,
          data.advance,
          data.nidPath,
          data.photoPath,
          data.isActive !== false ? 1 : 0,
        ]);
        return {
          success: true,
          data: { id: result?.lastInsertRowid, ...data },
        };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to create customer",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.CUSTOMER_GET_ALL, async () => {
      try {
        const sql = `
          SELECT c.*,
                 a.name as apartment_name, a.size as apartment_size, a.number_of_rooms,
                 f.name as floor_name, f.floor_number,
                 b.name as building_name, b.address as building_address
          FROM customers c
          LEFT JOIN apartments a ON c.id = a.current_customer_id
          LEFT JOIN floors f ON a.floor_id = f.id
          LEFT JOIN buildings b ON f.building_id = b.id
          ORDER BY c.created_at DESC
        `;
        const customers = this.databaseService?.query(sql) || [];
        return { success: true, data: customers };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to fetch customers",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.CUSTOMER_UPDATE, async (_, data) => {
      try {
        const sql = `
          UPDATE customers SET
            name = ?, address = ?, mobile = ?, occupation = ?,
            start_date = ?, rent = ?, advance = ?, nid_path = ?,
            photo_path = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
        this.databaseService?.execute(sql, [
          data.name,
          data.address,
          data.mobile,
          data.occupation,
          data.startDate,
          data.rent,
          data.advance,
          data.nidPath,
          data.photoPath,
          data.isActive ? 1 : 0,
          data.id,
        ]);
        return { success: true, data };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to update customer",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.CUSTOMER_DELETE, async (_, id) => {
      try {
        const sql = `DELETE FROM customers WHERE id = ?`;
        this.databaseService?.execute(sql, [id]);
        return { success: true };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to delete customer",
        };
      }
    });

    ipcMain.handle(IPC_CHANNELS.CUSTOMER_GET_AVAILABLE, async () => {
      try {
        const sql = `
          SELECT * FROM customers
          WHERE is_active = 0 OR id NOT IN (SELECT current_customer_id FROM apartments WHERE current_customer_id IS NOT NULL)
          ORDER BY name
        `;
        const customers = this.databaseService?.query(sql) || [];
        return { success: true, data: customers };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to fetch available customers",
        };
      }
    });

    // Get customer by ID
    ipcMain.handle(IPC_CHANNELS.CUSTOMER_GET_BY_ID, async (_, id) => {
      try {
        const sql = `
          SELECT c.*,
                 a.name as apartment_name, a.size as apartment_size, a.number_of_rooms,
                 f.name as floor_name, f.floor_number,
                 b.name as building_name, b.address as building_address
          FROM customers c
          LEFT JOIN apartments a ON c.id = a.current_customer_id
          LEFT JOIN floors f ON a.floor_id = f.id
          LEFT JOIN buildings b ON f.building_id = b.id
          WHERE c.id = ?
        `;
        const customer = this.databaseService?.query(sql, [id])?.[0];
        if (!customer) {
          return { success: false, error: "Customer not found" };
        }
        return { success: true, data: customer };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Failed to fetch customer",
        };
      }
    });

    // Get customer booking history
    ipcMain.handle(IPC_CHANNELS.CUSTOMER_GET_HISTORY, async (_, customerId) => {
      try {
        const sql = `
          SELECT ab.*,
                 a.name as apartment_name, a.size as apartment_size, a.number_of_rooms,
                 f.name as floor_name, f.floor_number,
                 b.name as building_name, b.address as building_address
          FROM apartment_bookings ab
          JOIN apartments a ON ab.apartment_id = a.id
          JOIN floors f ON a.floor_id = f.id
          JOIN buildings b ON f.building_id = b.id
          WHERE ab.customer_id = ?
          ORDER BY ab.created_at DESC
        `;
        const history = this.databaseService?.query(sql, [customerId]) || [];
        return { success: true, data: history };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to fetch customer history",
        };
      }
    });

    // Google Drive operations
    ipcMain.handle(IPC_CHANNELS.GOOGLE_AUTH, async () => {
      return this.googleDriveService?.authenticate();
    });

    ipcMain.handle(IPC_CHANNELS.GOOGLE_SYNC, async () => {
      return this.googleDriveService?.syncData();
    });

    // Dialog operations
    ipcMain.handle("dialog:openFile", async (_, options) => {
      const result = await dialog.showOpenDialog(this.mainWindow!, options);
      return result;
    });

    ipcMain.handle("dialog:saveFile", async (_, options) => {
      const result = await dialog.showSaveDialog(this.mainWindow!, options);
      return result;
    });

    // User operations (placeholder)
    ipcMain.handle(IPC_CHANNELS.USER_GET_CURRENT, async () => {
      try {
        // TODO: Implement user management
        return { success: true, data: { id: 1, name: "Admin User" } };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : "Failed to get current user",
        };
      }
    });

    ipcMain.handle("dialog:showMessage", async (_, options) => {
      const result = await dialog.showMessageBox(this.mainWindow!, options);
      return result;
    });
  }
}

// Create the application instance
new ElectronApp();
