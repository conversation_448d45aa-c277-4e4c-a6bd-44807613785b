import { contextBridge, ip<PERSON><PERSON><PERSON><PERSON> } from "electron";

// Import IPC channels
const IPC_CHANNELS = {
  // Database operations
  DB_INIT: "db:init",
  DB_QUERY: "db:query",
  DB_EXECUTE: "db:execute",

  // Building operations
  BUILDING_CREATE: "building:create",
  BUILDING_UPDATE: "building:update",
  BUILDING_DELETE: "building:delete",
  BUILDING_GET_ALL: "building:getAll",
  BUILDING_GET_BY_ID: "building:getById",
  BUILDING_GET_WITH_STATS: "building:getWithStats",

  // Floor operations
  FLOOR_CREATE: "floor:create",
  FLOOR_UPDATE: "floor:update",
  FLOOR_DELETE: "floor:delete",
  FLOOR_GET_BY_BUILDING: "floor:getByBuilding",
  FLOOR_AUTO_GENERATE: "floor:autoGenerate",

  // Apartment operations
  APARTMENT_CREATE: "apartment:create",
  APARTMENT_UPDATE: "apartment:update",
  APARTMENT_DELETE: "apartment:delete",
  APARTMENT_GET_BY_FLOOR: "apartment:getByFloor",
  APARTMENT_GET_WITH_DETAILS: "apartment:getWithDetails",
  APARTMENT_BOOK: "apartment:book",
  APARTMENT_CHECKOUT: "apartment:checkout",
  APARTMENT_GET_HISTORY: "apartment:getHistory",

  // Customer operations
  CUSTOMER_CREATE: "customer:create",
  CUSTOMER_UPDATE: "customer:update",
  CUSTOMER_DELETE: "customer:delete",
  CUSTOMER_GET_ALL: "customer:getAll",
  CUSTOMER_GET_BY_ID: "customer:getById",
  CUSTOMER_GET_AVAILABLE: "customer:getAvailable",
  CUSTOMER_GET_HISTORY: "customer:getHistory",

  // File operations
  FILE_UPLOAD: "file:upload",
  FILE_DELETE: "file:delete",
  FILE_GET_METADATA: "file:getMetadata",
  FILE_GENERATE_THUMBNAIL: "file:generateThumbnail",

  // Google Drive operations
  GOOGLE_AUTH: "google:auth",
  GOOGLE_SYNC: "google:sync",
  GOOGLE_UPLOAD: "google:upload",
  GOOGLE_DOWNLOAD: "google:download",

  // User operations
  USER_CREATE: "user:create",
  USER_UPDATE: "user:update",
  USER_GET_CURRENT: "user:getCurrent",

  // Search and reporting
  SEARCH_GLOBAL: "search:global",
  REPORT_GENERATE: "report:generate",

  // Data export/import
  EXPORT_DATA: "export:data",
  IMPORT_DATA: "import:data",
};

// Define the API that will be exposed to the renderer process
const electronAPI = {
  // Database operations
  database: {
    init: () => ipcRenderer.invoke(IPC_CHANNELS.DB_INIT),
    query: (sql: string, params?: any[]) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_QUERY, sql, params),
    execute: (sql: string, params?: any[]) =>
      ipcRenderer.invoke(IPC_CHANNELS.DB_EXECUTE, sql, params),
  },

  // Building operations
  buildings: {
    create: (data: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.BUILDING_CREATE, data),
    update: (data: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.BUILDING_UPDATE, data),
    delete: (id: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.BUILDING_DELETE, id),
    getAll: () => ipcRenderer.invoke(IPC_CHANNELS.BUILDING_GET_ALL),
    getById: (id: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.BUILDING_GET_BY_ID, id),
    getWithStats: () =>
      ipcRenderer.invoke(IPC_CHANNELS.BUILDING_GET_WITH_STATS),
  },

  // Floor operations
  floors: {
    create: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.FLOOR_CREATE, data),
    update: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.FLOOR_UPDATE, data),
    delete: (id: number) => ipcRenderer.invoke(IPC_CHANNELS.FLOOR_DELETE, id),
    getByBuilding: (buildingId: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.FLOOR_GET_BY_BUILDING, buildingId),
    autoGenerate: (buildingId: number, numberOfFloors: number) =>
      ipcRenderer.invoke(
        IPC_CHANNELS.FLOOR_AUTO_GENERATE,
        buildingId,
        numberOfFloors
      ),
  },

  // Apartment operations
  apartments: {
    create: (data: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.APARTMENT_CREATE, data),
    update: (data: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.APARTMENT_UPDATE, data),
    delete: (id: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.APARTMENT_DELETE, id),
    getByFloor: (floorId: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.APARTMENT_GET_BY_FLOOR, floorId),
    getWithDetails: (id: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.APARTMENT_GET_WITH_DETAILS, id),
    book: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.APARTMENT_BOOK, data),
    checkout: (data: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.APARTMENT_CHECKOUT, data),
    getHistory: (apartmentId: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.APARTMENT_GET_HISTORY, apartmentId),
  },

  // Customer operations
  customers: {
    create: (data: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.CUSTOMER_CREATE, data),
    update: (data: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.CUSTOMER_UPDATE, data),
    delete: (id: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.CUSTOMER_DELETE, id),
    getAll: () => ipcRenderer.invoke(IPC_CHANNELS.CUSTOMER_GET_ALL),
    getById: (id: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.CUSTOMER_GET_BY_ID, id),
    getAvailable: () => ipcRenderer.invoke(IPC_CHANNELS.CUSTOMER_GET_AVAILABLE),
    getHistory: (customerId: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.CUSTOMER_GET_HISTORY, customerId),
  },

  // File operations
  files: {
    upload: (
      fileData: Buffer,
      fileName: string,
      entityType: string,
      entityId: number,
      fieldName: string
    ) =>
      ipcRenderer.invoke(
        IPC_CHANNELS.FILE_UPLOAD,
        fileData,
        fileName,
        entityType,
        entityId,
        fieldName
      ),
    delete: (filePath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.FILE_DELETE, filePath),
    getMetadata: (entityType: string, entityId: number) =>
      ipcRenderer.invoke(IPC_CHANNELS.FILE_GET_METADATA, entityType, entityId),
    generateThumbnail: (filePath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.FILE_GENERATE_THUMBNAIL, filePath),
  },

  // Google Drive operations
  googleDrive: {
    authenticate: () => ipcRenderer.invoke(IPC_CHANNELS.GOOGLE_AUTH),
    sync: () => ipcRenderer.invoke(IPC_CHANNELS.GOOGLE_SYNC),
    upload: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.GOOGLE_UPLOAD, data),
    download: (fileId: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.GOOGLE_DOWNLOAD, fileId),
  },

  // User operations
  users: {
    create: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.USER_CREATE, data),
    update: (data: any) => ipcRenderer.invoke(IPC_CHANNELS.USER_UPDATE, data),
    getCurrent: () => ipcRenderer.invoke(IPC_CHANNELS.USER_GET_CURRENT),
  },

  // Search and reporting
  search: {
    global: (query: string, filters?: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.SEARCH_GLOBAL, query, filters),
  },

  reports: {
    generate: (type: string, options?: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.REPORT_GENERATE, type, options),
  },

  // Data export/import
  dataExport: {
    export: (options: any) =>
      ipcRenderer.invoke(IPC_CHANNELS.EXPORT_DATA, options),
    import: (filePath: string) =>
      ipcRenderer.invoke(IPC_CHANNELS.IMPORT_DATA, filePath),
  },

  // Dialog operations
  dialogs: {
    openFile: (options: any) => ipcRenderer.invoke("dialog:openFile", options),
    saveFile: (options: any) => ipcRenderer.invoke("dialog:saveFile", options),
    showMessage: (options: any) =>
      ipcRenderer.invoke("dialog:showMessage", options),
  },

  // Utility functions
  utils: {
    getAppVersion: () => ipcRenderer.invoke("app:getVersion"),
    getAppPath: () => ipcRenderer.invoke("app:getPath"),
    openExternal: (url: string) =>
      ipcRenderer.invoke("shell:openExternal", url),
  },
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld("electronAPI", electronAPI);

// Type declaration for the exposed API
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}
