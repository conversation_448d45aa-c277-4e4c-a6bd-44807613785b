import { ApiResponse } from '../../types/common';

export interface DashboardStats {
  totalBuildings: number;
  totalApartments: number;
  occupiedApartments: number;
  totalCustomers: number;
  monthlyRevenue: number;
  occupancyRate: number;
}

export class DashboardService {
  // Get dashboard statistics from the database
  static async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      // Get all statistics in parallel
      const [buildingsResponse, customersResponse] = await Promise.all([
        window.electronAPI.buildings.getWithStats(),
        window.electronAPI.customers.getAll(),
      ]);

      if (!buildingsResponse.success) {
        throw new Error(buildingsResponse.error || 'Failed to fetch buildings');
      }

      if (!customersResponse.success) {
        throw new Error(customersResponse.error || 'Failed to fetch customers');
      }

      const buildings = buildingsResponse.data || [];
      const customers = customersResponse.data || [];

      // Calculate statistics
      const totalBuildings = buildings.length;
      const totalApartments = buildings.reduce((sum: number, building: any) => 
        sum + (building.total_apartments || 0), 0);
      const occupiedApartments = buildings.reduce((sum: number, building: any) => 
        sum + (building.occupied_apartments || 0), 0);
      const totalCustomers = customers.filter((customer: any) => 
        customer.apartment_name // Only count customers with apartments
      ).length;
      const monthlyRevenue = buildings.reduce((sum: number, building: any) => 
        sum + (building.total_rent_collection || 0), 0);
      const occupancyRate = totalApartments > 0 ? 
        (occupiedApartments / totalApartments) * 100 : 0;

      const stats: DashboardStats = {
        totalBuildings,
        totalApartments,
        occupiedApartments,
        totalCustomers,
        monthlyRevenue,
        occupancyRate: Math.round(occupancyRate * 10) / 10, // Round to 1 decimal place
      };

      return { success: true, data: stats };
    } catch (error) {
      console.error('Error fetching dashboard statistics:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch dashboard statistics',
      };
    }
  }

  // Get recent activity (placeholder for future implementation)
  static async getRecentActivity(): Promise<ApiResponse<any[]>> {
    try {
      // TODO: Implement recent activity tracking
      // This could include recent customer additions, apartment bookings, etc.
      return { success: true, data: [] };
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch recent activity',
      };
    }
  }

  // Get monthly revenue trend (placeholder for future implementation)
  static async getMonthlyRevenueTrend(): Promise<ApiResponse<any[]>> {
    try {
      // TODO: Implement monthly revenue trend calculation
      // This would require tracking historical data
      return { success: true, data: [] };
    } catch (error) {
      console.error('Error fetching revenue trend:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch revenue trend',
      };
    }
  }

  // Get occupancy trend (placeholder for future implementation)
  static async getOccupancyTrend(): Promise<ApiResponse<any[]>> {
    try {
      // TODO: Implement occupancy trend calculation
      // This would require tracking historical occupancy data
      return { success: true, data: [] };
    } catch (error) {
      console.error('Error fetching occupancy trend:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch occupancy trend',
      };
    }
  }
}
